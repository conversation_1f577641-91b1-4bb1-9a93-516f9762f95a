<?php

namespace App\Modules\AccountCredit\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\AccountCredit\Requests\StoreStripeRequest;
use App\Modules\AccountCredit\Services\AccountCreditFundSelectionService;
use App\Modules\AccountCredit\Services\ViewAccountCreditService;
use Inertia\Inertia;

class AccountCreditController extends Controller
{
    public function index()
    {
        $data = ViewAccountCreditService::instance()->getIndexData();

        return Inertia::render('AccountBalance/Index', $data);
    }

    public function selectMethod()
    {
        return Inertia::render(
            'AccountBalance/SelectPaymentMethod',
            AccountCreditFundSelectionService::instance()->getFundSelectionData()
        );
    }

    public function addWireTransfer()
    {
        return Inertia::render('AccountBalance/AddWireTransfer');
    }

    public function storeStripe(StoreStripeRequest $request)
    {
        $request->store();
    }

    public function renderSuccessPage()
    {
        return Inertia::render(
            'AccountBalance/AccountBalanceAddFundsSuccessPage',
            [
                'initialSetup' => false,
                'message' => 'Funds have been successfully added',
            ]
        );
    }
}
