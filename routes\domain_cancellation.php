<?php

use App\Modules\DomainCancellationRequest\Controllers\CancellationController;
use Illuminate\Support\Facades\Route;

Route::middleware(
    [
        'auth',
        'auth.active',
        'account.setup'
    ]
)
    ->prefix('domain')
    ->group(
        function () {
            Route::post('/cancel', [CancellationController::class, 'cancellationRequest'])->name('domain.cancel');
        }
    );
