//* PACKAGES 
import React, {useState, useEffect} from 'react';
import { router, usePage } from '@inertiajs/react';

//* ICONS
//...

//* LAYOUTS 
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";

//* COMPONENTS
import AppUserProfileImageComponent from '@/Components/App/AppUserProfileImageComponent';
import PrimaryButton from '@/Components/PrimaryButton';
import UtilConvertDate from '@/Util/UtilConvertDate';

//* STATE
//...

//* UTILS
//... 

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function UserProfileIndex(
    {
        //! VARIABLES
        //...
        
        //! STATES 
        //...
        
        //! EVENTS
        //..,
    }
)
{
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES

    //! VARIABLES 
    const dataInformationProfile = 
    [
        {
            label: 'first name', 
            value: user.first_name, 
        }, 
        {
            label: 'last name', 
            value: user.last_name, 
        },
        {
            label: 'email', 
            value: user.email, 
        }
    ];
    
    const dataInformationAddress = 
    [
        {   
            label: 'apt, suite,  etc.', 
            value: user.unit, 
        },
        {
            label: 'street', 
            value: user.street, 
        }, 
        {
            label: 'city', 
            value: user.city
        },
        {
            label: 'state / province', 
            value: user.state_province
        },
        {
            label: 'postal code', 
            value: user.postal_code
        },  
        {
            label: 'country code', 
            value: user.country_code
        },  
    ];
    
    const dataInformationTimestamps = 
    [
        {
            label: 'created at', 
            value: UtilConvertDate(user.created_at), 
        }, 
        {
            label: 'updated at', 
            value: UtilConvertDate(user.updated_at), 
        },
    ] ;
    
    //! USE EFFECTS

    //! FUNCTIONS

    return (
        <AccountCenterLayout
            postRouteName={"user-profile.index"}
        >
            <div
                className='mx-auto container max-w-[900px] mt-10 flex flex-col space-y-4 gap-y-5'
            >
                <div
                    className='text-2xl font-bold'
                >
                    Profile 
                </div>
                <div
                    className='flex flex-col gap-y-8'
                >
                    <section
                        className='flex flex-col gap-5'
                    >
                        <div
                            className='font-semibold text-lg'
                        >
                            Profile Information
                        </div>
                        <div
                            className='flex'
                        >
                            <div
                                className='grid md:grid-cols-2 gap-5 grow'
                            >
                                {
                                    dataInformationProfile.map(
                                        (item, index) => 
                                        {
                                            return (
                                                <div
                                                    key={index}
                                                    className='flex flex-col'
                                                >
                                                    <div
                                                        className='font-semibold capitalize'
                                                    >
                                                        {item.label}
                                                    </div>
                                                    <div>
                                                        {item.value}
                                                    </div>
                                                </div>
                                            ); 
                                        }
                                    )
                                }
                            </div>
                            <div>
                                <AppUserProfileImageComponent
                                    user={user}
                                />
                            </div>
                        </div>
                    </section>

                    <hr />

                    <section
                        className='flex flex-col gap-5'
                    >
                        <div
                            className='font-semibold text-lg'
                        >
                            Address Information 
                        </div>
                        <div
                            className='grid md:grid-cols-2 xl:grid-cols-3 gap-5 grow'
                        >
                            {
                                dataInformationAddress.map(
                                    (item, index) => 
                                    {
                                        return (
                                            <div
                                                key={index}
                                                className='flex flex-col'
                                            >
                                                <div
                                                    className='font-semibold capitalize'
                                                >
                                                    {item.label}
                                                </div>
                                                <div>
                                                    {item.value}
                                                </div>
                                            </div>
                                        ); 
                                    }
                                )
                            }
                        </div>
                    </section>

                    <hr />

                    <section
                        className='flex flex-col gap-5'
                    >
                        <div
                            className='font-semibold text-lg'
                        >
                            Timestamp
                        </div>
                        <div
                            className='grid md:grid-cols-2 xl:grid-cols-3 gap-5 grow'
                        >
                            {
                                dataInformationTimestamps.map(
                                    (item, index) => 
                                    {
                                        return (
                                            <div
                                                key={index}
                                                className='flex flex-col'
                                            >
                                                <div
                                                    className='font-semibold capitalize'
                                                >
                                                    {item.label}
                                                </div>
                                                <div>
                                                    {item.value}
                                                </div>
                                            </div>
                                        ); 
                                    }
                                )
                            }
                        </div>
                    </section>
                </div>
                <div
                    className='flex justify-end'
                >
                    <PrimaryButton
                        className='flex justify-end'
                        onClick={() => router.get(route('user-profile.edit'))} 
                    >
                        Edit
                    </PrimaryButton>
                </div>
            </div>
        </AccountCenterLayout>
    );
}
