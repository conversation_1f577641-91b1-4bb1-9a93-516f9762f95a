import { Link, useForm, usePage } from "@inertiajs/react";
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import PrimaryButton from "@/Components/PrimaryButton";
import TextInput from "@/Components/TextInput";
import TextArea from "@/Components/TextArea";
import { getEventValue } from "@/Util/TargetInputEvent";
import { MdMessage, MdCall, MdLocationOn, MdEmail } from "react-icons/md";
import PageFooter from "@/Components/Policy/Footer"; //"../Components/Policy/Footer"
import PageHeader from "@/Components/Policy/Header"; //../Components/Policy/Header
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";

export default function DomainCancellation() {
    const { userOn } = usePage().props;



    const content = (
        <div className="bg-gray-100 min-h-svh">
            <PageHeader />
            <div className="flex justify-center w-full m-auto mb-20 mt-10">
                <img
                    className="hidden lg:block w-1/4"
                    src="/assets/images/order_confirmed.svg"
                    alt="background"
                />
            </div>
            <div className="justify-center w-2/4 m-auto mb-20 mt-10 ">
                <p className="text-lg">
                    Thank you for your request. You will receive an email
                    notification shortly regarding the status of your domain
                    deletion request. Our support team will aslso be in touch
                    via phone within 1-2 business days to confirm the request
                    and assist with any further steps.
                </p>
                <span className="flex justify-center w-full m-auto mb-20 mt-10">
                    <a
                        href="/domain"
                        className="justify-center text-2xl underline"
                    >
                        Go back to Domain
                    </a>
                </span>
            </div>
        </div>
    );
    return (
        <>
            {!userOn ? (
                <AccountCenterLayout
                    isReportAbusePage={true}
                    NewElementClassNamesIfHome="w-full"
                >
                    {content}
                </AccountCenterLayout>
            ) : (
                content
            )}
            <PageFooter />
        </>
    );
}
