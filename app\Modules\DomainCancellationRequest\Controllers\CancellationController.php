<?php

namespace App\Modules\DomainCancellationRequest\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\DomainCancellationRequest\Requests\CancellationRequest;
use Inertia\Inertia;
use Inertia\Response;

class CancellationController extends Controller
{
    public function cancellationRequest(CancellationRequest $request): Response
    {
        $request->cancellationRequest();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Thank you for your request. You will receive an email notification shortly regarding the status of your domain deletion request. Our support team will also be in touch via phone within 1-2 business days to confirm the request and assist with any further steps.',
            'postRouteName' => 'domain',
        ]);
    }
}
