[2025-08-20 02:05:20] local.INFO: <EMAIL> Created stripe intent: usd 1743  
[2025-08-20 02:05:20] local.ERROR: {"error":"ErrorException","message":"Undefined array key \"com\"","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/redeem\/pay","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\1xampp\\\\htdoc...', 209)
#1 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\1xampp\\\\htdoc...', 209)
#2 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(196): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::calculateTotalRegistryAmount(Array, Array, 'redemption_fee', Array, Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(106): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::getTotalRegistryAmount(Array, Array, 'REDEMPTION')
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService.php(66): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::checkRegistryBalance(Array, Array, 'REDEMPTION')
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest.php(35): App\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService->getRedemptionPaymentData(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Controllers\\RedemptionController.php(21): App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest->getRedemptionPaymentData()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\Domain\\Controllers\\RedemptionController->pay(Object(App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('pay', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Domain\\Controllers\\RedemptionController), 'pay')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-20 02:11:42] local.INFO: <EMAIL> Created stripe intent: usd 1743  
[2025-08-20 02:11:42] local.ERROR: {"error":"ErrorException","message":"Undefined array key \"com\"","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/redeem\/pay","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\1xampp\\\\htdoc...', 209)
#1 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\1xampp\\\\htdoc...', 209)
#2 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(196): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::calculateTotalRegistryAmount(Array, Array, 'redemption_fee', Array, Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(106): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::getTotalRegistryAmount(Array, Array, 'REDEMPTION')
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService.php(66): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::checkRegistryBalance(Array, Array, 'REDEMPTION')
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest.php(35): App\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService->getRedemptionPaymentData(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Controllers\\RedemptionController.php(21): App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest->getRedemptionPaymentData()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\Domain\\Controllers\\RedemptionController->pay(Object(App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('pay', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Domain\\Controllers\\RedemptionController), 'pay')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-20 02:17:48] local.ERROR: {"error":"Illuminate\\Database\\UniqueConstraintViolationException","message":"SQLSTATE[23505]: Unique violation: 7 ERROR:  duplicate key value violates unique constraint \"fees_pkey\"
DETAIL:  Key (id)=(1) already exists. (Connection: pgsql, SQL: insert into \"fees\" (\"type\", \"value\", \"created_at\", \"updated_at\") values (REDEMPTION, 200, 2025-08-20 02:17:48, 2025-08-20 02:17:48) returning \"id\")","url":"http:\/\/localhost\/strangedomains.local","code":"23505","ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"fe...', Array, Object(Closure))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('insert into \"fe...', Array, Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(382): Illuminate\\Database\\Connection->select('insert into \"fe...', Array, false)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"fe...', Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"fe...', Array, NULL)
#5 C:\\1xampp\\htdocs\\sd-client\\database\\migrations\\2025_01_30_000001_ensure_redemption_extension_fees_exist.php(20): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(447): Illuminate\\Database\\Connection->transaction(Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_01_30_0000...', Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_30_0000...', Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\1xampp\\\\htdoc...', 10, false)
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"}  
[2025-08-20 02:19:40] local.ERROR: {"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42701]: Duplicate column: 7 ERROR:  column \"admin_id\" of relation \"admin_notifications\" already exists (Connection: pgsql, SQL: alter table \"admin_notifications\" add column \"admin_id\" bigint null)","url":"http:\/\/localhost\/strangedomains.local","code":"42701","ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"ad...', Array, Object(Closure))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"ad...', Array, Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"ad...')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('admin_notificat...', Object(Closure))
#6 C:\\1xampp\\htdocs\\sd-client\\database\\migrations\\2025_07_24_065454_add_admin_id_to_admin_notifications_table.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(107): Illuminate\\Database\\Migrations\\Migration@anonymous->{closure}(Object(Illuminate\\Database\\Schema\\Blueprint))
#8 [internal function]: Illuminate\\Database\\Schema\\Blueprint->__construct(Object(Illuminate\\Database\\PostgresConnection), 'admin_notificat...', Object(Closure))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Data...')
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Data...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Data...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Data...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(636): Illuminate\\Foundation\\Application->make('Illuminate\\\\Data...', Array)
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->createBlueprint('admin_notificat...', Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('admin_notificat...', Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\database\\migrations\\2025_07_24_065454_add_admin_id_to_admin_notifications_table.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(447): Illuminate\\Database\\Connection->transaction(Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_24_0654...', Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_24_0654...', Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\1xampp\\\\htdoc...', 10, false)
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}"}  
[2025-08-20 02:21:35] local.INFO: <EMAIL> Created stripe intent: usd 1743  
[2025-08-20 02:21:35] local.ERROR: {"error":"ErrorException","message":"Undefined array key \"com\"","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/redeem\/pay","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined array...', 'C:\\\\1xampp\\\\htdoc...', 209)
#1 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined array...', 'C:\\\\1xampp\\\\htdoc...', 209)
#2 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(196): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::calculateTotalRegistryAmount(Array, Array, 'redemption_fee', Array, Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(106): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::getTotalRegistryAmount(Array, Array, 'REDEMPTION')
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService.php(66): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::checkRegistryBalance(Array, Array, 'REDEMPTION')
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest.php(35): App\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService->getRedemptionPaymentData(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Controllers\\RedemptionController.php(21): App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest->getRedemptionPaymentData()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\Domain\\Controllers\\RedemptionController->pay(Object(App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('pay', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Domain\\Controllers\\RedemptionController), 'pay')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-20 02:25:25] local.ERROR: {"error":"Illuminate\\Database\\UniqueConstraintViolationException","message":"SQLSTATE[23505]: Unique violation: 7 ERROR:  duplicate key value violates unique constraint \"fees_pkey\"
DETAIL:  Key (id)=(2) already exists. (Connection: pgsql, SQL: insert into \"fees\" (\"type\", \"value\", \"created_at\", \"updated_at\") values (REDEMPTION, 100, 2025-08-20 02:25:25, 2025-08-20 02:25:25) returning \"id\")","url":"http:\/\/localhost\/strangedomains.local","code":"23505","ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"fe...', Array, Object(Closure))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('insert into \"fe...', Array, Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(382): Illuminate\\Database\\Connection->select('insert into \"fe...', Array, false)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"fe...', Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"fe...', Array, NULL)
#5 C:\\1xampp\\htdocs\\sd-client\\database\\migrations\\2025_08_20_021924_add_redemption_feetype_to_fees_table.php(15): Illuminate\\Database\\Query\\Builder->insertGetId(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(447): Illuminate\\Database\\Connection->transaction(Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_08_20_0219...', Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_20_0219...', Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\1xampp\\\\htdoc...', 10, false)
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}"}  
[2025-08-20 02:26:46] local.INFO: <EMAIL> Created stripe intent: usd 22663  
[2025-08-20 02:27:19] local.INFO: <EMAIL> Created payment invoice with value 216.36,216.36,PAID,1,13  
[2025-08-20 02:27:19] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":216.36,"paid_amount":216.36,"status":"PAID","total_payment_node":1,"payment_service_id":13,"updated_at":"2025-08-20T02:27:19.000000Z","created_at":"2025-08-20T02:27:19.000000Z","id":6}  
[2025-08-20 02:27:19] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-20 02:27:19] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-20 02:27:20] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-20 02:27:20] local.INFO: <EMAIL> Updated domain id 72 to status IN_PROCESS  
[2025-08-20 02:27:44] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/redeem. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/redeem","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-20 02:28:30] local.INFO: <EMAIL> Domain redemption started: radiantrover.com  
[2025-08-20 02:28:33] local.ERROR: 403 : Authorization error  
[2025-08-20 02:28:37] local.ERROR: 403 : Authorization error  
[2025-08-20 02:28:37] local.ERROR: <EMAIL> Restore Report failed: EPP Error 2201: Authorization error  
[2025-08-20 02:28:37] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:28:37] local.INFO: <EMAIL> Updated domain id 72 to status PENDING  
[2025-08-20 02:30:02] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-20 02:30:03] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 02:30:03] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 02:30:04] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-20 02:30:04] local.INFO: PostAutoRenewalGracePeriodHandler: Skipping for 6 hours...  
[2025-08-20 02:30:04] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 02:30:04] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 02:30:04] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 02:30:05] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:30:05] local.INFO: GeneralNotification: done  
[2025-08-20 02:30:06] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:30:06] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:30:07] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 02:30:07] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 02:30:07] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:30:07] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:30:08] local.INFO: AccountCreditor: Running...  
[2025-08-20 02:30:08] local.INFO: AccountCreditor: Done  
[2025-08-20 02:30:10] local.INFO: SessionPollChecker: Running...  
[2025-08-20 02:30:10] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:30:11] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:30:11] local.INFO: SessionPollChecker: Done  
[2025-08-20 02:31:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:31:01] local.INFO: GeneralNotification: done  
[2025-08-20 02:31:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:31:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:31:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:31:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:32:02] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 02:32:03] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 02:32:04] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:32:05] local.INFO: GeneralNotification: done  
[2025-08-20 02:32:06] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:32:07] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:32:08] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 02:32:08] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 02:32:09] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:32:09] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:32:10] local.INFO: AccountCreditor: Running...  
[2025-08-20 02:32:11] local.INFO: AccountCreditor: Done  
[2025-08-20 02:32:12] local.INFO: SessionPollChecker: Running...  
[2025-08-20 02:32:13] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:32:13] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:32:13] local.INFO: SessionPollChecker: Done  
[2025-08-20 02:33:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 02:33:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 02:33:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 02:33:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:33:02] local.INFO: GeneralNotification: done  
[2025-08-20 02:33:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:33:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:33:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:33:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:52:16] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:16] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:19] local.ERROR: 400 : teachmeone.net found but not available  
[2025-08-20 02:52:19] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:19] local.INFO: <EMAIL> Updated domain id 5 to status PENDING  
[2025-08-20 02:52:19] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:19] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:20] local.ERROR: 404 : Object does not exist  
[2025-08-20 02:52:20] local.INFO: <EMAIL> Updated domain id 4 to status NOT_AVAILABLE  
[2025-08-20 02:52:21] local.ERROR: <EMAIL> Object does not exist: teachmeone.com  
[2025-08-20 02:52:21] local.ERROR: {"error":"TypeError","message":"App\\Modules\\Domain\\Services\\JobServices\\JobDispatchService::{closure}(): Argument #1 ($e) must be of type Throwable, null given","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 [internal function]: App\\Modules\\Domain\\Services\\JobServices\\JobDispatchService::{closure}(NULL)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Signed.php(43): call_user_func_array(Object(Closure), Array)
#2 [internal function]: Laravel\\SerializableClosure\\Serializers\\Signed->__invoke(NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\serializable-closure\\src\\SerializableClosure.php(39): call_user_func_array(Object(Laravel\\SerializableClosure\\Serializers\\Signed), Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Queueable.php(303): Laravel\\SerializableClosure\\SerializableClosure->__invoke(NULL)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(271): App\\Modules\\Domain\\Jobs\\UpdateEppDomain->Illuminate\\Bus\\{closure}(Object(Laravel\\SerializableClosure\\SerializableClosure), 0)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Queueable.php(302): Illuminate\\Support\\Collection->each(Object(Closure))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(334): App\\Modules\\Domain\\Jobs\\UpdateEppDomain->invokeChainCatchCallbacks(NULL)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(297): Illuminate\\Queue\\CallQueuedHandler->ensureChainCatchCallbacksAreInvoked('c48b7004-48b3-4...', Object(App\\Modules\\Domain\\Jobs\\UpdateEppDomain), NULL)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(254): Illuminate\\Queue\\CallQueuedHandler->failed(Array, NULL, 'c48b7004-48b3-4...', Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(219): Illuminate\\Queue\\Jobs\\Job->failed(NULL)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\InteractsWithQueue.php(61): Illuminate\\Queue\\Jobs\\Job->fail(NULL)
#12 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Jobs\\UpdateEppDomain.php(98): App\\Modules\\Domain\\Jobs\\UpdateEppDomain->fail()
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Modules\\Domain\\Jobs\\UpdateEppDomain->handle()
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Container\\Container->call(Array)
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Modules\\Domain\\Jobs\\UpdateEppDomain))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Modules\\Domain\\Jobs\\UpdateEppDomain))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Modules\\Domain\\Jobs\\UpdateEppDomain), false)
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Modules\\Domain\\Jobs\\UpdateEppDomain))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Modules\\Domain\\Jobs\\UpdateEppDomain))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Modules\\Domain\\Jobs\\UpdateEppDomain))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(444): Illuminate\\Queue\\Jobs\\Job->fire()
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(394): Illuminate\\Queue\\Worker->process('domain_update_j...', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(180): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'domain_update_j...', Object(Illuminate\\Queue\\WorkerOptions))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon('domain_update_j...', 'VERISIGN-UPDATE...', Object(Illuminate\\Queue\\WorkerOptions))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('domain_update_j...', 'VERISIGN-UPDATE...')
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 {main}"}  
[2025-08-20 02:52:21] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:21] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:22] local.ERROR: 400 : handlerme.net found but not available  
[2025-08-20 02:52:22] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:22] local.INFO: <EMAIL> Updated domain id 2 to status PENDING  
[2025-08-20 02:52:22] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:22] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:27] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:52:28] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:28] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:29] local.ERROR: 400 : porqueto.net found but not available  
[2025-08-20 02:52:29] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:29] local.INFO: <EMAIL> Updated domain id 7 to status PENDING  
[2025-08-20 02:52:29] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:29] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:30] local.ERROR: 400 : porqueto.com found but not available  
[2025-08-20 02:52:30] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:30] local.INFO: <EMAIL> Updated domain id 6 to status PENDING  
[2025-08-20 02:52:31] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:31] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:31] local.ERROR: 400 : poktare.com found but not available  
[2025-08-20 02:52:31] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:31] local.INFO: <EMAIL> Updated domain id 9 to status PENDING  
[2025-08-20 02:52:31] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:31] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:32] local.ERROR: 400 : hajasawa.com found but not available  
[2025-08-20 02:52:32] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:32] local.INFO: <EMAIL> Updated domain id 10 to status PENDING  
[2025-08-20 02:52:32] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:32] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:36] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:52:36] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:36] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:39] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:52:39] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:39] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:40] local.ERROR: 400 : coffeedosage.net found but not available  
[2025-08-20 02:52:40] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:40] local.INFO: <EMAIL> Updated domain id 110 to status PENDING  
[2025-08-20 02:52:40] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:40] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:40] local.ERROR: 400 : coffeedose.com found but not available  
[2025-08-20 02:52:40] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:40] local.INFO: <EMAIL> Updated domain id 109 to status PENDING  
[2025-08-20 02:52:40] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:40] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:46] local.ERROR: 403 : Authorization error  
[2025-08-20 02:52:46] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:46] local.INFO: <EMAIL> Updated domain id 108 to status PENDING  
[2025-08-20 02:52:46] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:46] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:47] local.ERROR: 400 : jcdonutsph.com found but not available  
[2025-08-20 02:52:47] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:47] local.INFO: <EMAIL> Updated domain id 107 to status PENDING  
[2025-08-20 02:52:47] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:47] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:53] local.ERROR: 403 : Authorization error  
[2025-08-20 02:52:53] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:53] local.INFO: <EMAIL> Updated domain id 106 to status PENDING  
[2025-08-20 02:52:53] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:53] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:52:59] local.ERROR: 403 : Authorization error  
[2025-08-20 02:52:59] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:52:59] local.INFO: <EMAIL> Updated domain id 105 to status PENDING  
[2025-08-20 02:52:59] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:52:59] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:04] local.ERROR: 403 : Authorization error  
[2025-08-20 02:53:04] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:04] local.INFO: <EMAIL> Updated domain id 104 to status PENDING  
[2025-08-20 02:53:04] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:04] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:05] local.ERROR: 400 : sumasamasila.com found but not available  
[2025-08-20 02:53:05] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:05] local.INFO: <EMAIL> Updated domain id 103 to status PENDING  
[2025-08-20 02:53:05] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:05] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:06] local.ERROR: 400 : midnightvortex.net found but not available  
[2025-08-20 02:53:06] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:06] local.INFO: <EMAIL> Updated domain id 102 to status PENDING  
[2025-08-20 02:53:06] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:06] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:06] local.ERROR: 400 : pwdforyou.net found but not available  
[2025-08-20 02:53:06] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:06] local.INFO: <EMAIL> Updated domain id 101 to status PENDING  
[2025-08-20 02:53:06] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:06] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:09] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:09] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:09] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:10] local.ERROR: 400 : cdforfree.com found but not available  
[2025-08-20 02:53:10] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:10] local.INFO: <EMAIL> Updated domain id 99 to status PENDING  
[2025-08-20 02:53:10] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:10] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:12] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:12] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:12] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:13] local.ERROR: 400 : redmonday.net found but not available  
[2025-08-20 02:53:13] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:13] local.INFO: <EMAIL> Updated domain id 97 to status PENDING  
[2025-08-20 02:53:13] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:13] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:16] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:16] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:16] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:18] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:18] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:18] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:24] local.ERROR: 403 : Authorization error  
[2025-08-20 02:53:24] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:24] local.INFO: <EMAIL> Updated domain id 94 to status PENDING  
[2025-08-20 02:53:24] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:24] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:30] local.ERROR: 403 : Authorization error  
[2025-08-20 02:53:30] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:30] local.INFO: <EMAIL> Updated domain id 93 to status PENDING  
[2025-08-20 02:53:30] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:30] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:36] local.ERROR: 403 : Authorization error  
[2025-08-20 02:53:36] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:36] local.INFO: <EMAIL> Updated domain id 92 to status PENDING  
[2025-08-20 02:53:36] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:36] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:38] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:38] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:38] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:40] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:40] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:40] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:43] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:43] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:43] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:45] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:45] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:45] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:48] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:48] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:48] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:51] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:51] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:51] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:54] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:54] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:54] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:55] local.ERROR: 400 : stellarcoral.net found but not available  
[2025-08-20 02:53:55] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:55] local.INFO: <EMAIL> Updated domain id 84 to status PENDING  
[2025-08-20 02:53:55] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:55] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:58] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:53:58] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:58] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:59] local.ERROR: 400 : chipsbyp.com found but not available  
[2025-08-20 02:53:59] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:59] local.INFO: <EMAIL> Updated domain id 82 to status PENDING  
[2025-08-20 02:53:59] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:59] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:53:59] local.ERROR: 400 : rapidpenguin.com found but not available  
[2025-08-20 02:53:59] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:53:59] local.INFO: <EMAIL> Updated domain id 81 to status PENDING  
[2025-08-20 02:53:59] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:53:59] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:02] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:54:02] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:02] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:05] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:54:05] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:05] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:07] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:54:07] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:07] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:13] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:13] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:13] local.INFO: <EMAIL> Updated domain id 77 to status PENDING  
[2025-08-20 02:54:13] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:13] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:13] local.ERROR: 400 : celestialpulse.com found but not available  
[2025-08-20 02:54:13] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:13] local.INFO: <EMAIL> Updated domain id 76 to status PENDING  
[2025-08-20 02:54:13] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:13] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:16] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:54:16] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:16] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:16] local.ERROR: 400 : velocityviper.com found but not available  
[2025-08-20 02:54:16] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:16] local.INFO: <EMAIL> Updated domain id 74 to status PENDING  
[2025-08-20 02:54:16] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:16] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:22] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:22] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:22] local.INFO: <EMAIL> Updated domain id 73 to status PENDING  
[2025-08-20 02:54:22] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:22] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:23] local.ERROR: 400 : radiantrover.com found but not available  
[2025-08-20 02:54:23] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:23] local.INFO: <EMAIL> Updated domain id 72 to status PENDING  
[2025-08-20 02:54:23] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:23] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:29] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:29] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:29] local.INFO: <EMAIL> Updated domain id 71 to status PENDING  
[2025-08-20 02:54:29] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:29] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:34] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:34] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:34] local.INFO: <EMAIL> Updated domain id 70 to status PENDING  
[2025-08-20 02:54:34] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:34] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:40] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:40] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:40] local.INFO: <EMAIL> Updated domain id 69 to status PENDING  
[2025-08-20 02:54:40] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:40] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:46] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:46] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:46] local.INFO: <EMAIL> Updated domain id 68 to status PENDING  
[2025-08-20 02:54:46] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:46] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:52] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:52] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:52] local.INFO: <EMAIL> Updated domain id 67 to status PENDING  
[2025-08-20 02:54:52] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:52] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:54:58] local.ERROR: 403 : Authorization error  
[2025-08-20 02:54:58] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:54:58] local.INFO: <EMAIL> Updated domain id 66 to status PENDING  
[2025-08-20 02:54:58] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:54:58] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:01] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-20 02:55:01] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-20 02:55:01] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-20 02:55:02] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-20 02:55:02] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-20 02:55:02] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-20 02:55:02] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-20 02:55:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:55:02] local.INFO: GeneralNotification: done  
[2025-08-20 02:55:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:55:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:55:04] local.ERROR: 403 : Authorization error  
[2025-08-20 02:55:04] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:55:04] local.INFO: <EMAIL> Updated domain id 65 to status PENDING  
[2025-08-20 02:55:04] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:04] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:55:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:55:10] local.ERROR: 403 : Authorization error  
[2025-08-20 02:55:10] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:55:10] local.INFO: <EMAIL> Updated domain id 64 to status PENDING  
[2025-08-20 02:55:10] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:10] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:16] local.ERROR: 403 : Authorization error  
[2025-08-20 02:55:16] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:55:16] local.INFO: <EMAIL> Updated domain id 63 to status PENDING  
[2025-08-20 02:55:16] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:16] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:21] local.ERROR: 403 : Authorization error  
[2025-08-20 02:55:21] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:55:21] local.INFO: <EMAIL> Updated domain id 62 to status PENDING  
[2025-08-20 02:55:21] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:21] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:27] local.ERROR: 403 : Authorization error  
[2025-08-20 02:55:27] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:55:27] local.INFO: <EMAIL> Updated domain id 61 to status PENDING  
[2025-08-20 02:55:27] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:27] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:28] local.ERROR: 400 : aurorabuzz.net found but not available  
[2025-08-20 02:55:28] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:55:28] local.INFO: <EMAIL> Updated domain id 60 to status PENDING  
[2025-08-20 02:55:28] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:28] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:29] local.ERROR: 400 : aurorabuzz.com found but not available  
[2025-08-20 02:55:29] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:55:29] local.INFO: <EMAIL> Updated domain id 59 to status PENDING  
[2025-08-20 02:55:29] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:29] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:32] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:32] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:32] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:36] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:36] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:36] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:38] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:38] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:38] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:41] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:41] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:41] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:44] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:44] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:44] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:46] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:46] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:46] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:49] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:49] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:49] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:51] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:51] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:51] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:54] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:54] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:54] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:57] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:57] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:57] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:55:59] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:55:59] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:55:59] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:02] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:02] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:02] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:02] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 02:56:02] local.INFO: JobRetryScheduler: found 1, retrying jobs...  
[2025-08-20 02:56:04] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:56:04] local.INFO: GeneralNotification: done  
[2025-08-20 02:56:04] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:04] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:04] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:05] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:56:05] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:56:06] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 02:56:06] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 02:56:07] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:56:07] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:56:07] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:07] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:07] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:08] local.INFO: AccountCreditor: Running...  
[2025-08-20 02:56:09] local.INFO: AccountCreditor: Done  
[2025-08-20 02:56:10] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:10] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:10] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:11] local.INFO: SessionPollChecker: Running...  
[2025-08-20 02:56:12] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:56:12] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:12] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:12] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:13] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:56:13] local.INFO: SessionPollChecker: Done  
[2025-08-20 02:56:14] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:14] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:14] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:17] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:17] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:17] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:19] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:19] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:19] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:22] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:22] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:22] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:24] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:24] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:24] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:43] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:43] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:48] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:48] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:48] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:51] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:51] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:51] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:53] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:53] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:53] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:56] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:56] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:56] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:56:58] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:56:58] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:56:58] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:00] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:00] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:00] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 02:57:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 02:57:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 02:57:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:57:02] local.INFO: GeneralNotification: done  
[2025-08-20 02:57:03] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:03] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:03] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:57:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:57:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:57:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:57:05] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:05] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:05] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:07] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:07] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:07] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:10] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:10] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:10] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:12] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:12] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:12] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:14] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:14] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:14] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:17] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:17] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:17] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:47] local.ERROR: Guest User cURL error 28: Operation timed out after 30009 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://130.211.227.70:8001/verisign/v3_1/domain/info  
[2025-08-20 02:57:47] local.ERROR: <EMAIL> Error Unknown  
[2025-08-20 02:57:47] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:47] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:52] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:52] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:52] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:55] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:55] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:55] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:57:57] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:57:57] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:57:57] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 02:58:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 02:58:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 02:58:01] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:01] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:01] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:58:02] local.INFO: GeneralNotification: done  
[2025-08-20 02:58:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:58:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:58:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 02:58:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 02:58:04] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:04] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:04] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:58:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 02:58:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 02:58:05] local.INFO: AccountCreditor: Done  
[2025-08-20 02:58:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 02:58:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:58:07] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:07] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:07] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 02:58:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 02:58:10] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:10] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:10] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:12] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:12] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:12] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:16] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:16] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:16] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:18] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:18] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:18] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:19] local.ERROR: 400 : transfer.com found but not available  
[2025-08-20 02:58:19] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:58:19] local.INFO: <EMAIL> Updated domain id 12 to status PENDING  
[2025-08-20 02:58:19] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:19] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:24] local.ERROR: 403 : Authorization error  
[2025-08-20 02:58:24] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:58:24] local.INFO: <EMAIL> Updated domain id 11 to status PENDING  
[2025-08-20 02:58:24] local.ERROR: {"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\Domain\\Jobs\\UpdateEppDomain has been attempted too many times.","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(809): Illuminate\\Queue\\MaxAttemptsExceededException::forJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(530): Illuminate\\Queue\\Worker->maxAttemptsExceededException(Object(Illuminate\\Queue\\Jobs\\DatabaseJob))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(433): Illuminate\\Queue\\Worker->markJobAsFailedIfAlreadyExceedsMaxAttempts('domain_update_j...', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 1)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(394): Illuminate\\Queue\\Worker->process('domain_update_j...', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(180): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'domain_update_j...', Object(Illuminate\\Queue\\WorkerOptions))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon('domain_update_j...', 'VERISIGN-UPDATE...', Object(Illuminate\\Queue\\WorkerOptions))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('domain_update_j...', 'VERISIGN-UPDATE...')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}"}  
[2025-08-20 02:58:24] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:24] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:30] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:58:30] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:30] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:30] local.ERROR: 400 : porqueto.org found but not available  
[2025-08-20 02:58:30] local.ERROR: <EMAIL> Retry  
[2025-08-20 02:58:30] local.INFO: <EMAIL> Updated domain id 8 to status PENDING  
[2025-08-20 02:58:30] local.INFO: <EMAIL> Domain details update start...  
[2025-08-20 02:58:30] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 02:58:33] local.INFO: <EMAIL> Domain update end...  
[2025-08-20 02:59:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 02:59:01] local.INFO: GeneralNotification: done  
[2025-08-20 02:59:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 02:59:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 02:59:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 02:59:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:00:02] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-20 03:00:03] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:00:03] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:00:03] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:00:04] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-20 03:00:04] local.INFO: PostAutoRenewalGracePeriodHandler: Skipping for 6 hours...  
[2025-08-20 03:00:04] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:00:04] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:00:04] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:00:05] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:00:05] local.INFO: GeneralNotification: done  
[2025-08-20 03:00:06] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:00:06] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:00:06] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:00:06] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:00:07] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:00:07] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:00:08] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:00:08] local.INFO: AccountCreditor: Done  
[2025-08-20 03:00:10] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:00:11] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:00:11] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:00:11] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:01:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:01:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:01:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:01:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:01:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:01:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:02:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:02:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:02:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:02:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:02:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:02:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:02:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:02:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:02:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:02:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:02:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:02:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:02:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:02:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:02:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:02:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:02:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:03:00] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:03:00] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:03:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:03:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:03:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:03:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:03:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:03:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:03:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:04:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:04:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:04:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:04:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:04:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:04:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:04:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:04:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:04:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:04:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:04:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:04:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:04:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:04:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:04:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:04:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:04:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:05:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:05:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:05:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:05:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:05:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:05:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:06:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:06:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:06:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:06:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:06:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:06:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:06:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:06:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:06:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:06:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:06:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:06:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:06:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:06:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:06:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:06:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:06:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:06:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:06:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:06:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:07:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:07:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:07:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:07:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:07:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:07:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:08:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:08:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:08:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:08:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:08:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:08:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:08:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:08:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:08:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:08:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:08:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:08:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:08:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:08:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:08:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:08:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:08:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:09:00] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:09:00] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:09:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:09:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:09:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:09:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:09:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:09:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:09:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:10:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:10:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:10:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:10:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:10:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:10:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:10:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:10:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:10:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:10:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:10:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:10:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:10:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:10:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:10:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:10:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:10:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:11:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:11:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:11:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:11:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:11:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:11:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:12:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:12:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:12:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:12:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:12:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:12:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:12:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:12:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:12:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:12:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:12:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:12:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:12:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:12:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:12:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:12:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:12:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:12:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:12:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:12:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:13:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:13:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:13:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:13:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:13:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:13:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:14:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:14:01] local.INFO: JobRetryScheduler: found 5, retrying jobs...  
[2025-08-20 03:14:01] local.INFO: JobRetryScheduler: done  
[2025-08-20 03:14:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:14:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:14:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:14:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:14:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:14:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:14:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:14:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:14:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:14:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:14:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:14:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:14:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:14:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:15:01] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-20 03:15:01] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-20 03:15:01] local.INFO: PostAutoRenewalGracePeriodHandler: Skipping for 6 hours...  
[2025-08-20 03:15:02] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:15:02] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:15:02] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:15:03] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:15:03] local.INFO: GeneralNotification: done  
[2025-08-20 03:15:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:15:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:15:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:15:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:16:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:16:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:16:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:16:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:16:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:16:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:16:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:16:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:16:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:16:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:16:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:16:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:16:05] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:16:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:16:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:16:06] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:17:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:17:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:17:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:17:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:17:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:17:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:18:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:18:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:18:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:18:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:18:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:18:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:18:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:18:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:18:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:18:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:18:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:18:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:18:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:18:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:18:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:18:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:18:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:18:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:18:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:19:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:19:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:19:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:19:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:19:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:19:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:20:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:20:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:20:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:20:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:20:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:20:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:20:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:20:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:20:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:20:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:20:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:20:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:20:05] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:20:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:20:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:20:06] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:21:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:21:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:21:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:21:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:21:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:21:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:21:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:21:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:21:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:22:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:22:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:22:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:22:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:22:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:22:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:22:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:22:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:22:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:22:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:22:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:22:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:22:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:22:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:22:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:22:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:23:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:23:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:23:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:23:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:23:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:23:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:24:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:24:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:24:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:24:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:24:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:24:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:24:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:24:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:24:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:24:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:24:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:24:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:24:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:24:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:24:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:24:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:24:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:24:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:24:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:25:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:25:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:25:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:25:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:25:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:25:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:26:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:26:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:26:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:26:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:26:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:26:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:26:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:26:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:26:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:26:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:26:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:26:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:26:05] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:26:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:26:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:26:06] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:27:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:27:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:27:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:27:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:27:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:27:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:27:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:27:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:27:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:28:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:28:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:28:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:28:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:28:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:28:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:28:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:28:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:28:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:28:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:28:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:28:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:28:05] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:28:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:28:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:28:06] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:29:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:29:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:29:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:29:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:29:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:29:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:30:00] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-20 03:30:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:30:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:30:02] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-20 03:30:02] local.INFO: PostAutoRenewalGracePeriodHandler: Skipping for 6 hours...  
[2025-08-20 03:30:02] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:30:02] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:30:03] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:30:03] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:30:03] local.INFO: GeneralNotification: done  
[2025-08-20 03:30:04] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:30:04] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:30:05] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:30:05] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:30:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:30:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:30:06] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:30:06] local.INFO: AccountCreditor: Done  
[2025-08-20 03:30:07] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:30:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:30:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:30:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:31:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:31:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:31:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:31:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:31:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:31:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:32:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:32:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:32:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:32:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:32:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:32:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:32:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:32:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:32:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:32:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:32:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:32:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:32:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:32:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:32:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:32:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:33:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:33:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:33:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:33:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:33:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:33:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:33:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:33:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:33:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:34:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:34:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:34:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:34:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:34:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:34:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:34:02] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:34:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:34:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:34:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:34:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:34:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:34:05] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:34:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:34:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:34:06] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:35:00] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:35:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:35:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:35:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:35:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:35:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:36:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:36:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:36:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:36:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:36:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:36:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:36:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:36:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:36:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:36:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:36:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:36:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:36:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:36:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:36:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:36:07] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:36:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:36:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:36:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:37:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:37:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:37:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:37:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:37:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:37:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:38:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:38:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:38:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:38:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:38:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:38:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:38:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:38:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:38:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:38:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:38:04] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:38:04] local.INFO: AccountCreditor: Done  
[2025-08-20 03:38:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:38:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:38:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:38:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:39:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:39:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:39:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:39:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:39:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:39:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:39:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:39:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:39:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:40:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:40:02] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:40:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:40:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:40:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:40:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:40:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:40:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:40:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:40:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:40:06] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:40:06] local.INFO: AccountCreditor: Done  
[2025-08-20 03:40:08] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:40:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:40:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:40:09] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:41:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:41:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:41:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:41:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:41:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:41:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:42:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:42:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:42:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:42:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:42:02] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:42:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:42:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:42:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:42:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:42:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:42:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:42:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:42:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:42:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:42:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:42:07] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:42:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:42:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:42:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:43:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:43:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:43:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:43:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:43:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:43:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:43:13] local.INFO: <EMAIL> Created stripe intent: usd 22663  
[2025-08-20 03:44:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:44:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:44:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:44:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:44:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:44:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:44:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:44:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:44:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:44:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:44:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:44:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:44:07] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:44:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:44:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:44:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:44:31] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/redeem\/confirm. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/redeem\/confirm","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-20 03:44:45] local.INFO: <EMAIL> Created stripe intent: usd 33979  
[2025-08-20 03:45:01] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-20 03:45:02] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-20 03:45:02] local.INFO: PostAutoRenewalGracePeriodHandler: Skipping for 6 hours...  
[2025-08-20 03:45:03] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:45:03] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:45:03] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:45:04] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:45:04] local.INFO: GeneralNotification: done  
[2025-08-20 03:45:06] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:45:06] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:45:07] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:45:08] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:46:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:46:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:46:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:46:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:46:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:46:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:46:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:46:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:46:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:46:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:46:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:46:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:46:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:46:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:46:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:46:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:47:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:47:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:47:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:47:04] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:47:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:47:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:48:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:48:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:48:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:48:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:48:02] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:48:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:48:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:48:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:48:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:48:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:48:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:48:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:48:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:48:05] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:48:05] local.INFO: AccountCreditor: Done  
[2025-08-20 03:48:07] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:48:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:48:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:48:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:49:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:49:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:49:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:49:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:49:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:49:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:50:02] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:50:02] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:50:03] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:50:03] local.INFO: GeneralNotification: done  
[2025-08-20 03:50:04] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:50:04] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:50:05] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:50:05] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:50:06] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:50:06] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:50:07] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:50:07] local.INFO: AccountCreditor: Done  
[2025-08-20 03:50:09] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:50:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:50:10] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:50:10] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:50:55] local.ERROR: {"error":"TypeError","message":"Unsupported operand types: int + stdClass","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/redeem\/pay","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService.php(60): App\\Modules\\Payment\\Services\\PaymentFeeService::getOtherRedemptionFees(Array)
#1 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest.php(35): App\\Modules\\Domain\\Services\\UpdateServices\\RedemptionDomainService->getRedemptionPaymentData(Array)
#2 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Domain\\Controllers\\RedemptionController.php(21): App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest->getRedemptionPaymentData()
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\Domain\\Controllers\\RedemptionController->pay(Object(App\\Modules\\Domain\\Requests\\Redemption\\RedemptionPayRequest))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('pay', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Domain\\Controllers\\RedemptionController), 'pay')
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 {main}"}  
[2025-08-20 03:51:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:51:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:51:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:51:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:51:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:51:04] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:51:04] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:51:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:51:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:51:37] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/domain","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-20 03:51:37] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/domain","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-20 03:51:51] local.INFO: <EMAIL> Created stripe intent: usd 2598  
[2025-08-20 03:52:02] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:52:02] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:52:04] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:52:04] local.INFO: GeneralNotification: done  
[2025-08-20 03:52:05] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:52:05] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:52:06] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:52:06] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:52:07] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:52:07] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:52:08] local.INFO: AccountCreditor: Running...  
[2025-08-20 03:52:08] local.INFO: AccountCreditor: Done  
[2025-08-20 03:52:10] local.INFO: SessionPollChecker: Running...  
[2025-08-20 03:52:10] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:52:10] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 03:52:10] local.INFO: SessionPollChecker: Done  
[2025-08-20 03:53:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:53:01] local.INFO: GeneralNotification: done  
[2025-08-20 03:53:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:53:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:53:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:53:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:54:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 03:54:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 03:54:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 03:54:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 03:54:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 03:54:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 03:54:02] local.INFO: GeneralNotification: done  
[2025-08-20 03:54:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 03:54:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 03:54:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 03:54:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 03:54:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 03:54:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 03:54:06] local.INFO: AccountCreditor: Running...  
[2025-08-20 04:55:45] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/login","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Aut...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Aut...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Aut...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Aut...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-20 04:55:47] local.INFO: <EMAIL> successful login at 127.0.0.1  
[2025-08-20 05:00:22] local.INFO: <EMAIL> Created stripe intent: usd 33979  
[2025-08-20 05:00:47] local.INFO: <EMAIL> Created stripe intent: usd 45295  
[2025-08-20 05:05:21] local.INFO: <EMAIL> Created stripe intent: usd 13914  
[2025-08-20 05:10:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 05:10:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 05:10:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:10:02] local.INFO: GeneralNotification: done  
[2025-08-20 05:10:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:10:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:10:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 05:10:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 05:10:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:10:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:10:07] local.INFO: SessionPollChecker: Running...  
[2025-08-20 05:10:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:10:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:10:09] local.INFO: SessionPollChecker: Done  
[2025-08-20 05:11:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:11:01] local.INFO: GeneralNotification: done  
[2025-08-20 05:11:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:11:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:11:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:11:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:12:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 05:12:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 05:12:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 05:12:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 05:12:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 05:12:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:12:02] local.INFO: GeneralNotification: done  
[2025-08-20 05:12:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:12:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:12:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 05:12:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 05:12:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:12:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:12:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 05:12:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:12:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:12:07] local.INFO: SessionPollChecker: Done  
[2025-08-20 05:13:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:13:01] local.INFO: GeneralNotification: done  
[2025-08-20 05:13:02] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:13:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:13:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:13:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:14:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 05:14:02] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 05:14:03] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:14:03] local.INFO: GeneralNotification: done  
[2025-08-20 05:14:04] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:14:04] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:14:05] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 05:14:05] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 05:14:07] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:14:07] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:14:10] local.INFO: SessionPollChecker: Running...  
[2025-08-20 05:14:11] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:14:11] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:14:11] local.INFO: SessionPollChecker: Done  
[2025-08-20 05:15:01] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-20 05:15:02] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-20 05:15:02] local.INFO: PostAutoRenewalGracePeriodHandler: Skipping for 6 hours...  
[2025-08-20 05:15:03] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 05:15:03] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 05:15:03] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 05:15:04] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:15:04] local.INFO: GeneralNotification: done  
[2025-08-20 05:15:06] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:15:07] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:15:08] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:15:08] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:16:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 05:16:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 05:16:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:16:02] local.INFO: GeneralNotification: done  
[2025-08-20 05:16:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:16:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:16:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 05:16:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 05:16:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:16:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:16:06] local.INFO: SessionPollChecker: Running...  
[2025-08-20 05:16:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:16:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:16:08] local.INFO: SessionPollChecker: Done  
[2025-08-20 05:17:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:17:01] local.INFO: GeneralNotification: done  
[2025-08-20 05:17:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:17:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:17:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:17:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:18:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 05:18:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 05:18:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-20 05:18:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-20 05:18:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-20 05:18:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:18:02] local.INFO: GeneralNotification: done  
[2025-08-20 05:18:03] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:18:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:18:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-20 05:18:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-20 05:18:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:18:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:18:05] local.INFO: SessionPollChecker: Running...  
[2025-08-20 05:18:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:18:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-20 05:18:06] local.INFO: SessionPollChecker: Done  
[2025-08-20 05:19:01] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:19:01] local.INFO: GeneralNotification: done  
[2025-08-20 05:19:01] local.INFO: AfternicDomainTask: Running...  
[2025-08-20 05:19:01] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-08-20 05:19:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-20 05:19:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-20 05:20:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-20 05:20:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-20 05:20:02] local.INFO: GeneralNotification: Running...  
[2025-08-20 05:20:02] local.INFO: GeneralNotification: done  
[2025-08-20 05:57:42] local.INFO: <EMAIL> Created stripe intent: usd 12203  
[2025-08-20 05:57:51] local.INFO: <EMAIL> Created stripe intent: usd 12203  
[2025-08-20 05:57:58] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3Ry51uAYkyIIc8ES0OF3gGO1","object":"payment_intent","amount":12203,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":12203,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3Ry51uAYkyIIc8ES0OF3gGO1_secret_XLMQWxIQLlGuX1GISGKCxxLa1","confirmation_method":"automatic","created":1755669470,"currency":"usd","customer":"cus_St9MLUIEnG0Q8M","description":null,"excluded_payment_method_types":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3Ry51uAYkyIIc8ES0cb2lTwv","object":"charge","amount":12203,"amount_captured":12203,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3Ry51uAYkyIIc8ES06jPEHE8","object":"balance_transaction","amount":12203,"available_on":1755820800,"balance_type":"payments","created":1755669476,"currency":"usd","description":null,"exchange_rate":null,"fee":384,"fee_details":[{"amount":384,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":11819,"reporting_category":"charge","source":"ch_3Ry51uAYkyIIc8ES0cb2lTwv","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":"Juls","phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1755669470,"currency":"usd","customer":"cus_St9MLUIEnG0Q8M","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":49,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3Ry51uAYkyIIc8ES0OF3gGO1","payment_method":"pm_1RxN3fAYkyIIc8ESlWLLrH5S","payment_method_details":{"card":{"amount_authorized":12203,"authorization_code":"972687","brand":"visa","capture_before":1756274270,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":null},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":12203,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKOXHlcUGMgYvkabCBOw6LBYj03EPz19Ivs8959y75-T4xT_QO_c3x4lgRTViLRbwEpTq5rYxtusli4Js","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RxN3fAYkyIIc8ESlWLLrH5S","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-08-20 05:57:58] local.INFO: getStripeIntentById balance:: {"id":"txn_3Ry51uAYkyIIc8ES06jPEHE8","object":"balance_transaction","amount":12203,"available_on":1755820800,"balance_type":"payments","created":1755669476,"currency":"usd","description":null,"exchange_rate":null,"fee":384,"fee_details":[{"amount":384,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":11819,"reporting_category":"charge","source":"ch_3Ry51uAYkyIIc8ES0cb2lTwv","status":"pending","type":"charge"}  
[2025-08-20 05:57:58] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"type":"REDEMPTION","user_id":7,"other_fees":{"domain_count":1,"privacy_count":0,"bill_total":116.36,"icann_fee":0.36,"year_sum":2,"redemption_total":100,"renewal_total":16,"redemption_fee":{"com":{"id":16,"extension_id":1,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"com","tld_id":1,"price":100},"net":{"id":17,"extension_id":2,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"net","tld_id":2,"price":100},"org":{"id":18,"extension_id":3,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"org","tld_id":3,"price":100}},"renewal_fee":{"com":{"id":2,"extension_id":1,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"com","tld_id":1,"price":8},"net":{"id":7,"extension_id":2,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"net","tld_id":2,"price":8},"org":{"id":12,"extension_id":3,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"org","tld_id":3,"price":8}}},"payment_intent":"pi_3Ry51uAYkyIIc8ES0OF3gGO1","total_amount":100,"total_payment_node":1,"paid_amount":116.36,"gross_amount":122.03,"bill_amount":116.36,"net_amount":118.19,"service_fee":3.84,"adjustments":1.83,"charge_id":"ch_3Ry51uAYkyIIc8ES0cb2lTwv"}  
[2025-08-20 05:57:58] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":122.03,"net_amount":118.19,"service_fee":3.84,"charge_id":"ch_3Ry51uAYkyIIc8ES0cb2lTwv","payment_intent":"pi_3Ry51uAYkyIIc8ES0OF3gGO1"}  
[2025-08-20 05:57:58] local.INFO: <EMAIL> Created payment invoice with value 116.36,122.03,PAID,1,14  
[2025-08-20 05:57:58] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":116.36,"paid_amount":122.03,"status":"PAID","total_payment_node":1,"payment_service_id":14,"updated_at":"2025-08-20T05:57:58.000000Z","created_at":"2025-08-20T05:57:58.000000Z","id":7}  
[2025-08-20 05:57:58] local.INFO: <EMAIL> Updated stripe intent for : 7-7-1  
[2025-08-20 05:57:59] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-20 05:57:59] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-20 05:57:59] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-20 05:58:00] local.INFO: <EMAIL> Updated domain id 95 to status IN_PROCESS  
[2025-08-20 05:58:56] local.INFO: <EMAIL> Domain redemption started: biogenesis.com  
[2025-08-20 05:59:03] local.ERROR: 403 : Object status prohibits operation  
[2025-08-20 05:59:03] local.INFO: <EMAIL> Updated domain id 95 to status ACTIVE  
[2025-08-20 05:59:03] local.INFO: <EMAIL> Updated registered domain 26 to OWNED status  
[2025-08-20 05:59:03] local.INFO: <EMAIL> Update payment nodes with registered domain id 26 column status with value COMPLETED  
[2025-08-20 05:59:05] local.INFO: Domain History: Domain "biogenesis.com" redemption completed <NAME_EMAIL>.  
[2025-08-20 06:26:39] local.INFO: <EMAIL> Created stripe intent: usd 16481  
[2025-08-20 06:26:59] local.INFO: <EMAIL> Created payment invoice with value 157.26,157.26,PAID,1,15  
[2025-08-20 06:26:59] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":157.26,"paid_amount":157.26,"status":"PAID","total_payment_node":1,"payment_service_id":15,"updated_at":"2025-08-20T06:26:59.000000Z","created_at":"2025-08-20T06:26:59.000000Z","id":8}  
[2025-08-20 06:26:59] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-20 06:26:59] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-20 06:26:59] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-20 06:26:59] local.INFO: <EMAIL> Updated domain id 96 to status IN_PROCESS  
[2025-08-20 06:27:34] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/redeem. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/redeem","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-20 06:28:46] local.INFO: <EMAIL> Domain redemption started: biogenesis.net  
[2025-08-20 06:28:53] local.ERROR: 403 : Object status prohibits operation  
[2025-08-20 06:28:53] local.INFO: <EMAIL> Updated domain id 96 to status ACTIVE  
[2025-08-20 06:28:53] local.INFO: <EMAIL> Updated registered domain 25 to OWNED status  
[2025-08-20 06:28:53] local.INFO: <EMAIL> Update payment nodes with registered domain id 25 column status with value COMPLETED  
[2025-08-20 06:28:54] local.INFO: Domain History: Domain "biogenesis.net" redemption completed <NAME_EMAIL>.  
[2025-08-20 06:34:42] local.INFO: <EMAIL> Created stripe intent: usd 2598  
[2025-08-20 06:34:50] local.INFO: <EMAIL> Created stripe intent: usd 2598  
[2025-08-20 06:34:57] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3Ry5biAYkyIIc8ES046ETBKC","object":"payment_intent","amount":2598,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":2598,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3Ry5biAYkyIIc8ES046ETBKC_secret_dYSQrOqc1ODKYxlmpr2eHe367","confirmation_method":"automatic","created":1755671690,"currency":"usd","customer":"cus_St9MLUIEnG0Q8M","description":null,"excluded_payment_method_types":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3Ry5biAYkyIIc8ES040ohJ6U","object":"charge","amount":2598,"amount_captured":2598,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3Ry5biAYkyIIc8ES0g9xqlw9","object":"balance_transaction","amount":2598,"available_on":1755820800,"balance_type":"payments","created":1755671695,"currency":"usd","description":null,"exchange_rate":null,"fee":105,"fee_details":[{"amount":105,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":2493,"reporting_category":"charge","source":"ch_3Ry5biAYkyIIc8ES040ohJ6U","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":"Juls","phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1755671690,"currency":"usd","customer":"cus_St9MLUIEnG0Q8M","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":14,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3Ry5biAYkyIIc8ES046ETBKC","payment_method":"pm_1RxN3fAYkyIIc8ESlWLLrH5S","payment_method_details":{"card":{"amount_authorized":2598,"authorization_code":"686797","brand":"visa","capture_before":1756276490,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":null},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":2598,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKJHZlcUGMgZsMZCFhBM6LBaab0z3OCvQxVDeyiMheVUOSqLdK8h00owznylJMfRtGzkng6rp2xYqh3wx","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RxN3fAYkyIIc8ESlWLLrH5S","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-08-20 06:34:57] local.INFO: getStripeIntentById balance:: {"id":"txn_3Ry5biAYkyIIc8ES0g9xqlw9","object":"balance_transaction","amount":2598,"available_on":1755820800,"balance_type":"payments","created":1755671695,"currency":"usd","description":null,"exchange_rate":null,"fee":105,"fee_details":[{"amount":105,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":2493,"reporting_category":"charge","source":"ch_3Ry5biAYkyIIc8ES040ohJ6U","status":"pending","type":"charge"}  
[2025-08-20 06:34:57] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"type":"RENEW","user_id":7,"other_fees":{"domain_count":1,"privacy_count":0,"bill_total":24.54,"icann_fee":0.54,"year_sum":3,"renewal_total":24,"penalty_total":0,"renewal_fee":{"com":{"id":2,"extension_id":1,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"com","tld_id":1,"price":8},"net":{"id":7,"extension_id":2,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"net","tld_id":2,"price":8},"org":{"id":12,"extension_id":3,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"org","tld_id":3,"price":8}}},"payment_intent":"pi_3Ry5biAYkyIIc8ES046ETBKC","total_amount":24,"total_payment_node":1,"paid_amount":24.54,"gross_amount":25.98,"bill_amount":24.54,"net_amount":24.93,"service_fee":1.05,"adjustments":0.39,"charge_id":"ch_3Ry5biAYkyIIc8ES040ohJ6U"}  
[2025-08-20 06:34:57] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":25.98,"net_amount":24.93,"service_fee":1.05,"charge_id":"ch_3Ry5biAYkyIIc8ES040ohJ6U","payment_intent":"pi_3Ry5biAYkyIIc8ES046ETBKC"}  
[2025-08-20 06:34:57] local.INFO: <EMAIL> Created payment invoice with value 24.54,25.98,PAID,1,16  
[2025-08-20 06:34:57] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":24.54,"paid_amount":25.98,"status":"PAID","total_payment_node":1,"payment_service_id":16,"updated_at":"2025-08-20T06:34:57.000000Z","created_at":"2025-08-20T06:34:57.000000Z","id":9}  
[2025-08-20 06:34:58] local.INFO: <EMAIL> Updated stripe intent for : 7-9-1  
[2025-08-20 06:34:58] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-20 06:34:58] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-20 06:34:58] local.INFO: Payment summary created: Payment Invoice - Renewal  
[2025-08-20 06:34:58] local.INFO: <EMAIL> Updated domain id 113 to status IN_PROCESS  
[2025-08-20 06:35:33] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/renew. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/renew","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-20 06:35:42] local.INFO: <EMAIL> Domain <NAME_EMAIL> start...  
[2025-08-20 06:35:46] local.INFO: <EMAIL> Update payment nodes with registered domain id 111 column status with value COMPLETED  
[2025-08-20 06:35:47] local.INFO: Guest User updated the expiration of the domain limittestme.org.  
[2025-08-20 06:35:47] local.INFO: <EMAIL> Domain <NAME_EMAIL> end...  
[2025-08-20 06:35:47] local.INFO: Domain History: Domain "limittestme.org" renewed for 3 year(s)  
[2025-08-20 06:36:16] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:17] local.ERROR: <EMAIL> Retry  
[2025-08-20 06:36:17] local.INFO: <EMAIL> Updated domain id 1 to status PENDING  
[2025-08-20 06:36:17] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:18] local.INFO: <EMAIL> Updated domainlimittestme.net  
[2025-08-20 06:36:19] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:20] local.INFO: <EMAIL> Updated domainlimittestme.com  
[2025-08-20 06:36:20] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:21] local.ERROR: 400 : cdforfree.net found but not available  
[2025-08-20 06:36:21] local.ERROR: <EMAIL> Retry  
[2025-08-20 06:36:21] local.INFO: <EMAIL> Updated domain id 100 to status PENDING  
[2025-08-20 06:36:21] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:21] local.ERROR: 400 : redmonday.com found but not available  
[2025-08-20 06:36:21] local.ERROR: <EMAIL> Retry  
[2025-08-20 06:36:21] local.INFO: <EMAIL> Updated domain id 98 to status PENDING  
[2025-08-20 06:36:21] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:22] local.INFO: <EMAIL> Updated domainbiogenesis.net  
[2025-08-20 06:36:22] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:24] local.INFO: <EMAIL> Updated domainbiogenesis.com  
[2025-08-20 06:36:24] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:24] local.INFO: <EMAIL> Updated domainorasistime.com  
[2025-08-20 06:36:25] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:25] local.INFO: <EMAIL> Updated domainthosearetimes.net  
[2025-08-20 06:36:25] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:26] local.INFO: <EMAIL> Updated domainorasistime.net  
[2025-08-20 06:36:26] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:27] local.INFO: <EMAIL> Updated domaindreamydolphin.com  
[2025-08-20 06:36:27] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:29] local.INFO: <EMAIL> Updated domaindreamydolphin.net  
[2025-08-20 06:36:29] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:30] local.INFO: <EMAIL> Updated domainstrangedomainstest.com  
[2025-08-20 06:36:30] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:31] local.INFO: <EMAIL> Updated domainstellarcoral.com  
[2025-08-20 06:36:31] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:32] local.INFO: <EMAIL> Updated domainchipsbyc.com  
[2025-08-20 06:36:32] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:32] local.INFO: <EMAIL> Updated domainchipsbyp.net  
[2025-08-20 06:36:33] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:33] local.INFO: <EMAIL> Updated domainlorebyaudio.net  
[2025-08-20 06:36:33] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:34] local.INFO: <EMAIL> Updated domainhopealliance.com  
[2025-08-20 06:36:34] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:35] local.INFO: <EMAIL> Updated domainstranged.net  
[2025-08-20 06:36:35] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:36] local.INFO: <EMAIL> Updated domainpirateradiojams.com  
[2025-08-20 06:36:36] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:36] local.INFO: <EMAIL> Updated domaintestdomaincheckbox.com  
[2025-08-20 06:36:37] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:37] local.INFO: <EMAIL> Updated domaintestdomaincheckbox.net  
[2025-08-20 06:36:37] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:39] local.INFO: <EMAIL> Updated domainitsidakarla.com  
[2025-08-20 06:36:39] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:40] local.INFO: <EMAIL> Updated domainitsidakarla.net  
[2025-08-20 06:36:40] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:41] local.INFO: <EMAIL> Updated domainnameseparated.com  
[2025-08-20 06:36:41] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:41] local.INFO: <EMAIL> Updated domainnameseparated.net  
[2025-08-20 06:36:42] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:42] local.INFO: <EMAIL> Updated domainstrangeida.com  
[2025-08-20 06:36:43] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:43] local.INFO: <EMAIL> Updated domainstrangeida.net  
[2025-08-20 06:36:43] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:44] local.INFO: <EMAIL> Updated domainkarla.net  
[2025-08-20 06:36:44] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:45] local.INFO: <EMAIL> Updated domainsearchyourdomainsnow.com  
[2025-08-20 06:36:45] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:46] local.INFO: <EMAIL> Updated domaintimelesstreasurehunts.com  
[2025-08-20 06:36:46] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:47] local.INFO: <EMAIL> Updated domaintimelesstreasurehunts.net  
[2025-08-20 06:36:47] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:47] local.INFO: <EMAIL> Updated domainmindfulnessmatrix.com  
[2025-08-20 06:36:47] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:48] local.INFO: <EMAIL> Updated domainmindfulnessmatrix.net  
[2025-08-20 06:36:48] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:49] local.INFO: <EMAIL> Updated domainidak.com  
[2025-08-20 06:36:49] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:50] local.INFO: <EMAIL> Updated domainidak.net  
[2025-08-20 06:36:50] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:50] local.INFO: <EMAIL> Updated domainstrangedomains.net  
[2025-08-20 06:36:51] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:51] local.INFO: <EMAIL> Updated domainsourgrape.com  
[2025-08-20 06:36:51] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:52] local.INFO: <EMAIL> Updated domainstrange.net  
[2025-08-20 06:36:52] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:53] local.INFO: <EMAIL> Updated domaingetdomains54.net  
[2025-08-20 06:36:53] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:54] local.INFO: <EMAIL> Updated domainasdqweqw.com  
[2025-08-20 06:36:54] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:54] local.INFO: <EMAIL> Updated domainasdqweqw.net  
[2025-08-20 06:36:54] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:55] local.INFO: <EMAIL> Updated domainitskarla.net  
[2025-08-20 06:36:55] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:56] local.INFO: <EMAIL> Updated domainitskarla.com  
[2025-08-20 06:36:56] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:57] local.INFO: <EMAIL> Updated domainbiogenic7u.net  
[2025-08-20 06:36:57] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:57] local.INFO: <EMAIL> Updated domainbiogenic7u.com  
[2025-08-20 06:36:58] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:58] local.INFO: <EMAIL> Updated domainstranged5.net  
[2025-08-20 06:36:58] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:36:59] local.INFO: <EMAIL> Updated domainstranged5.com  
[2025-08-20 06:36:59] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:00] local.INFO: <EMAIL> Updated domainqid1.net  
[2025-08-20 06:37:00] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:00] local.INFO: <EMAIL> Updated domainbiogenic3u.com  
[2025-08-20 06:37:01] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:01] local.INFO: <EMAIL> Updated domainbiogenic3u.net  
[2025-08-20 06:37:01] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:02] local.INFO: <EMAIL> Updated domaincheckdomains4u.net  
[2025-08-20 06:37:02] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:03] local.INFO: <EMAIL> Updated domaincheckdomains4u.com  
[2025-08-20 06:37:03] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:04] local.INFO: <EMAIL> Updated domainbiogenic2u.net  
[2025-08-20 06:37:04] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:04] local.INFO: <EMAIL> Updated domainstranged2.net  
[2025-08-20 06:37:05] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:05] local.INFO: <EMAIL> Updated domainstranged2.com  
[2025-08-20 06:37:05] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:06] local.INFO: <EMAIL> Updated domainbiogenic1u.net  
[2025-08-20 06:37:06] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:07] local.INFO: <EMAIL> Updated domainbiogenic1u.com  
[2025-08-20 06:37:07] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:08] local.INFO: <EMAIL> Updated domaingetdomains1u.com  
[2025-08-20 06:37:08] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:08] local.INFO: <EMAIL> Updated domaingetdomains2u.net  
[2025-08-20 06:37:09] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:09] local.INFO: <EMAIL> Updated domaingetdomains5u.net  
[2025-08-20 06:37:09] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:10] local.INFO: <EMAIL> Updated domaingetdomains4u.com  
[2025-08-20 06:37:10] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:11] local.INFO: <EMAIL> Updated domainwhysoserious1.com  
[2025-08-20 06:37:11] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:12] local.INFO: <EMAIL> Updated domainbiogenesis.com  
[2025-08-20 06:37:12] local.ERROR: <EMAIL> Retry  
[2025-08-20 06:37:12] local.INFO: <EMAIL> Updated domain id 95 to status PENDING  
[2025-08-20 06:37:12] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:13] local.INFO: <EMAIL> Updated domainbiogenesis.net  
[2025-08-20 06:37:13] local.ERROR: <EMAIL> Retry  
[2025-08-20 06:37:13] local.INFO: <EMAIL> Updated domain id 96 to status PENDING  
[2025-08-20 06:37:13] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:15] local.ERROR: <EMAIL> Retry  
[2025-08-20 06:37:15] local.INFO: <EMAIL> Updated domain id 3 to status PENDING  
[2025-08-20 06:37:15] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:15] local.INFO: <EMAIL> Updated domainlimittestme.org  
[2025-08-20 06:37:15] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:37:16] local.INFO: <EMAIL> Updated domainlimittestme.org  
[2025-08-20 06:39:57] local.INFO: <EMAIL> Created stripe intent: usd 13059  
[2025-08-20 06:40:19] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3Ry5gfAYkyIIc8ES0KY5N14p","object":"payment_intent","amount":13059,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":13059,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3Ry5gfAYkyIIc8ES0KY5N14p_secret_em7s91lRRDlz1dBRwVys1zElK","confirmation_method":"automatic","created":1755671997,"currency":"usd","customer":null,"description":null,"excluded_payment_method_types":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3Ry5gfAYkyIIc8ES0x3Mmksj","object":"charge","amount":13059,"amount_captured":13059,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3Ry5gfAYkyIIc8ES0iNLWle7","object":"balance_transaction","amount":13059,"available_on":1755820800,"balance_type":"payments","created":1755672017,"currency":"usd","description":null,"exchange_rate":null,"fee":409,"fee_details":[{"amount":409,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":12650,"reporting_category":"charge","source":"ch_3Ry5gfAYkyIIc8ES0x3Mmksj","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":null,"phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1755672011,"currency":"usd","customer":null,"description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":25,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3Ry5gfAYkyIIc8ES0KY5N14p","payment_method":"pm_1Ry5gtAYkyIIc8ES4zvw2HMy","payment_method_details":{"card":{"amount_authorized":13059,"authorization_code":"349087","brand":"visa","capture_before":1756276811,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":"pass"},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":13059,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKNLblcUGMgbJYw9Gbcc6LBaQbjqJgNS_drh4KCRdGj858N0NwyDUQkdJLguRqqDuQLlm66mmdupFg1i7","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1Ry5gtAYkyIIc8ES4zvw2HMy","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-08-20 06:40:19] local.INFO: getStripeIntentById balance:: {"id":"txn_3Ry5gfAYkyIIc8ES0iNLWle7","object":"balance_transaction","amount":13059,"available_on":1755820800,"balance_type":"payments","created":1755672017,"currency":"usd","description":null,"exchange_rate":null,"fee":409,"fee_details":[{"amount":409,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":12650,"reporting_category":"charge","source":"ch_3Ry5gfAYkyIIc8ES0x3Mmksj","status":"pending","type":"charge"}  
[2025-08-20 06:40:19] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"type":"REDEMPTION","user_id":7,"other_fees":{"domain_count":1,"privacy_count":0,"bill_total":124.54,"icann_fee":0.54,"year_sum":3,"redemption_total":100,"renewal_total":24,"redemption_fee":{"com":{"id":16,"extension_id":1,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"com","tld_id":1,"price":100},"net":{"id":17,"extension_id":2,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"net","tld_id":2,"price":100},"org":{"id":18,"extension_id":3,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"org","tld_id":3,"price":100}},"renewal_fee":{"com":{"id":2,"extension_id":1,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"com","tld_id":1,"price":8},"net":{"id":7,"extension_id":2,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"net","tld_id":2,"price":8},"org":{"id":12,"extension_id":3,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"org","tld_id":3,"price":8}},"epp_total":124},"payment_intent":"pi_3Ry5gfAYkyIIc8ES0KY5N14p","total_amount":124,"total_payment_node":1,"paid_amount":124.54,"gross_amount":130.59,"bill_amount":124.54,"net_amount":126.5,"service_fee":4.09,"adjustments":1.96,"charge_id":"ch_3Ry5gfAYkyIIc8ES0x3Mmksj"}  
[2025-08-20 06:40:19] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":130.59,"net_amount":126.5,"service_fee":4.09,"charge_id":"ch_3Ry5gfAYkyIIc8ES0x3Mmksj","payment_intent":"pi_3Ry5gfAYkyIIc8ES0KY5N14p"}  
[2025-08-20 06:40:19] local.INFO: <EMAIL> Created payment invoice with value 124.54,130.59,PAID,1,17  
[2025-08-20 06:40:19] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":124.54,"paid_amount":130.59,"status":"PAID","total_payment_node":1,"payment_service_id":17,"updated_at":"2025-08-20T06:40:19.000000Z","created_at":"2025-08-20T06:40:19.000000Z","id":10}  
[2025-08-20 06:40:19] local.INFO: <EMAIL> Updated stripe intent for : 7-10-1  
[2025-08-20 06:40:19] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-20 06:40:19] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-20 06:40:19] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-20 06:40:19] local.INFO: <EMAIL> Updated domain id 97 to status IN_PROCESS  
[2025-08-20 06:40:52] local.INFO: <EMAIL> Domain redemption started: redmonday.net  
[2025-08-20 06:40:59] local.ERROR: 403 : Object status prohibits operation  
[2025-08-20 06:40:59] local.INFO: <EMAIL> Updated domain id 97 to status ACTIVE  
[2025-08-20 06:40:59] local.INFO: <EMAIL> Updated registered domain 24 to OWNED status  
[2025-08-20 06:40:59] local.INFO: <EMAIL> Update payment nodes with registered domain id 24 column status with value COMPLETED  
[2025-08-20 06:41:01] local.INFO: Domain History: Domain "redmonday.net" redemption completed <NAME_EMAIL>.  
[2025-08-20 06:41:01] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 06:41:02] local.INFO: <EMAIL> Updated domainredmonday.net  
[2025-08-20 06:41:02] local.ERROR: <EMAIL> Retry  
[2025-08-20 06:41:02] local.INFO: <EMAIL> Updated domain id 97 to status PENDING  
[2025-08-20 07:01:34] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-20 07:01:34] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-20 07:02:47] local.INFO: <EMAIL> Created stripe intent: usd 13914  
[2025-08-20 07:03:04] local.INFO: <EMAIL> Created stripe intent: usd 13914  
[2025-08-20 07:03:11] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3Ry631AYkyIIc8ES1szv0V9H","object":"payment_intent","amount":13914,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":13914,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3Ry631AYkyIIc8ES1szv0V9H_secret_6FiyGpBZecncWKh09QEk8gWSV","confirmation_method":"automatic","created":1755673383,"currency":"usd","customer":"cus_St9MLUIEnG0Q8M","description":null,"excluded_payment_method_types":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3Ry631AYkyIIc8ES1tkEa3kR","object":"charge","amount":13914,"amount_captured":13914,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3Ry631AYkyIIc8ES1jb6wmtg","object":"balance_transaction","amount":13914,"available_on":1755820800,"balance_type":"payments","created":1755673389,"currency":"usd","description":null,"exchange_rate":null,"fee":434,"fee_details":[{"amount":434,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":13480,"reporting_category":"charge","source":"ch_3Ry631AYkyIIc8ES1tkEa3kR","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":"Juls","phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1755673384,"currency":"usd","customer":"cus_St9MLUIEnG0Q8M","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":6,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3Ry631AYkyIIc8ES1szv0V9H","payment_method":"pm_1RxN3fAYkyIIc8ESlWLLrH5S","payment_method_details":{"card":{"amount_authorized":13914,"authorization_code":"520239","brand":"visa","capture_before":1756278184,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":null},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":13914,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKK7mlcUGMgaiEb2kAJI6LBZNHrKVKNBkBBX_WpbBkOcncHCgirlGIkpcdSCzI1kDRAqJe64ZLyFZ5WP0","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RxN3fAYkyIIc8ESlWLLrH5S","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-08-20 07:03:11] local.INFO: getStripeIntentById balance:: {"id":"txn_3Ry631AYkyIIc8ES1jb6wmtg","object":"balance_transaction","amount":13914,"available_on":1755820800,"balance_type":"payments","created":1755673389,"currency":"usd","description":null,"exchange_rate":null,"fee":434,"fee_details":[{"amount":434,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":13480,"reporting_category":"charge","source":"ch_3Ry631AYkyIIc8ES1tkEa3kR","status":"pending","type":"charge"}  
[2025-08-20 07:03:11] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"type":"REDEMPTION","user_id":7,"other_fees":{"domain_count":1,"privacy_count":0,"bill_total":132.72,"icann_fee":0.72,"year_sum":4,"redemption_total":100,"renewal_total":32,"redemption_fee":{"com":{"id":16,"extension_id":1,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"com","tld_id":1,"price":100},"net":{"id":17,"extension_id":2,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"net","tld_id":2,"price":100},"org":{"id":18,"extension_id":3,"fee_id":6,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-20 02:26:12","updated_at":"2025-08-20 02:26:12","fee_type":"REDEMPTION","fee_value":"100","extension_name":"org","tld_id":3,"price":100}},"renewal_fee":{"com":{"id":2,"extension_id":1,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"com","tld_id":1,"price":8},"net":{"id":7,"extension_id":2,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"net","tld_id":2,"price":8},"org":{"id":12,"extension_id":3,"fee_id":2,"value":"0","is_default":true,"user_id":0,"deleted_at":null,"created_at":"2025-08-18 14:35:52","updated_at":"2025-08-18 14:35:52","fee_type":"RENEW","fee_value":"8","extension_name":"org","tld_id":3,"price":8}},"epp_total":132},"payment_intent":"pi_3Ry631AYkyIIc8ES1szv0V9H","total_amount":132,"total_payment_node":1,"paid_amount":132.72,"gross_amount":139.14,"bill_amount":132.72,"net_amount":134.8,"service_fee":4.34,"adjustments":2.08,"charge_id":"ch_3Ry631AYkyIIc8ES1tkEa3kR"}  
[2025-08-20 07:03:11] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":139.14,"net_amount":134.8,"service_fee":4.34,"charge_id":"ch_3Ry631AYkyIIc8ES1tkEa3kR","payment_intent":"pi_3Ry631AYkyIIc8ES1szv0V9H"}  
[2025-08-20 07:03:11] local.INFO: <EMAIL> Created payment invoice with value 132.72,139.14,PAID,1,18  
[2025-08-20 07:03:11] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":132.72,"paid_amount":139.14,"status":"PAID","total_payment_node":1,"payment_service_id":18,"updated_at":"2025-08-20T07:03:11.000000Z","created_at":"2025-08-20T07:03:11.000000Z","id":11}  
[2025-08-20 07:03:12] local.INFO: <EMAIL> Updated stripe intent for : 7-11-1  
[2025-08-20 07:03:12] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-20 07:03:12] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-20 07:03:12] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-20 07:03:12] local.INFO: <EMAIL> Updated domain id 99 to status IN_PROCESS  
[2025-08-20 07:03:44] local.INFO: <EMAIL> Domain redemption started: cdforfree.com  
[2025-08-20 07:03:53] local.ERROR: 403 : Object status prohibits operation  
[2025-08-20 07:03:53] local.INFO: <EMAIL> Updated domain id 99 to status ACTIVE  
[2025-08-20 07:03:53] local.INFO: <EMAIL> Updated registered domain 22 to OWNED status  
[2025-08-20 07:03:53] local.INFO: <EMAIL> Update payment nodes with registered domain id 22 column status with value COMPLETED  
[2025-08-20 07:03:54] local.INFO: Domain History: Domain "cdforfree.com" redemption completed <NAME_EMAIL>.  

[2025-08-20 07:06:56] local.INFO: <EMAIL> Checking if domain already exists...  
[2025-08-20 07:06:58] local.INFO: <EMAIL> Updated domaincdforfree.com  
[2025-08-20 07:06:58] local.ERROR: <EMAIL> Retry  
[2025-08-20 07:06:58] local.INFO: <EMAIL> Updated domain id 99 to status PENDING  
[2025-08-20 08:05:03] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-08-22 03:00:34] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/login","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Aut...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Aut...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Aut...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Aut...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-22 03:00:35] local.INFO: <EMAIL> successful login at 127.0.0.1  
[2025-08-22 07:00:10] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-08-22 07:02:40] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-22 07:02:41] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-22 07:18:26] testing.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route strangedomains.local could not be found.","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '\/', Array, Array, Array, Array)
#25 C:\\1xampp\\htdocs\\sd-client\\tests\\Feature\\ExampleTest.php(15): Illuminate\\Foundation\\Testing\\TestCase->get('\/')
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): Tests\\Feature\\ExampleTest->test_the_application_returns_a_successful_response()
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\ExampleTest))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\pestphp\\pest\\bin\\pest(192): {closure}()
#38 {main}"}  
[2025-08-22 08:31:06] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/notification\/unread-count","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-22 08:31:06] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/notification\/unread-count","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#16 {main}"}  
[2025-08-22 08:33:54] local.ERROR: {"error":"Illuminate\\Validation\\ValidationException","message":"The domain field is required.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/cancel","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(32): Illuminate\\Foundation\\Http\\FormRequest->failedValidation(Object(Illuminate\\Validation\\Validator))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\DomainCancellationRequest\\Requests\\CancellationRequest), Object(Illuminate\\Foundation\\Application))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\DomainCancellationRequest\\Requests\\CancellationRequest), Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Dom...', Object(App\\Modules\\DomainCancellationRequest\\Requests\\CancellationRequest))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Dom...', Object(App\\Modules\\DomainCancellationRequest\\Requests\\CancellationRequest))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Dom...', Array, true)
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Dom...', Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Dom...', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Dom...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\DomainCancellationRequest\\Controllers\\CancellationController), 'cancellationReq...')
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\DomainCancellationRequest\\Controllers\\CancellationController), 'cancellationReq...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\DomainCancellationRequest\\Controllers\\CancellationController), 'cancellationReq...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 {main}"}  
[2025-08-22 08:34:08] local.INFO: Domain History: Domain cancellation request <NAME_EMAIL>.  
