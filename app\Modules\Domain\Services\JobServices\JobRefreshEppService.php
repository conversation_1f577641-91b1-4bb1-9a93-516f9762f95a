<?php

namespace App\Modules\Domain\Services\JobServices;

use App\Events\UpdateDomainsTableEvent;
use App\Models\Domain;
use App\Models\RegisteredDomain;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Services\EppDomainService;
use App\Util\Constant\QueueErrorTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Config;

class JobRefreshEppService
{
    use UserLoggerTrait;

    private $dispatchDelayInSeconds = 180;

    private JobRecord $jobRecord;

    public static function instance(): self
    {
        $jobRefreshEppService = new self;

        return $jobRefreshEppService;
    }

    public function handle(JobRecord $record)
    {
        $this->jobRecord = $record;
        $this->handleRefreshDomain();
    }

    // PRIVATE FUNCTIONS

    private function handleRefreshDomain()
    {
        $isUpdated = false;

        switch ($this->jobRecord->updateType) {
            case DomainJobTypes::UPDATE_AFTER_REGISTER:
            case DomainJobTypes::UPDATE_RESTRICTIONS:
            case DomainJobTypes::UPDATE_RENEWAL:
            case DomainJobTypes::UPDATE_POST_AUTO_RENEWAL_GRACE_PERIOD:
                $isUpdated = $this->updateDomainInfo();
                break;
            default:
                $isUpdated = $this->handleUpdateSuccess();
                break;
        }

        $this->checkUpdateStatus($isUpdated);
    }

    private function checkUpdateStatus(bool $isUpdated)
    {
        if (! $isUpdated) {
            throw new Exception(QueueErrorTypes::RETRY);
        }

        $this->jobRecord->stopJobRetry(DomainStatus::ACTIVE);
        UpdateDomainsTableEvent::dispatch($this->jobRecord->userId);
    }

    private function handleUpdateSuccess()
    {
        $registerDomainUpdated = $this->updateRegisteredDomain($this->jobRecord->registeredDomain, $this->jobRecord->email);
        $domainUpdated = $this->updateDomainInfo();

        return $registerDomainUpdated && $domainUpdated;
    }

    private function updateRegisteredDomain(object $registeredDomain, string $email): bool
    {
        $id = $registeredDomain->id;
        unset($registeredDomain->id);
        $name = $registeredDomain->name;
        unset($registeredDomain->name);

        $updated = RegisteredDomain::when(is_int($id), function ($query) use ($id) {
            return $query->where('id', $id);
        })->update((array) $registeredDomain);

        if ($updated) {
            app(AuthLogger::class)->info($this->fromWho('Updated RegisteredDomain '.$name, $email));
        }

        return $updated;
    }

    private function updateDomainInfo()
    {
        $eppData = $this->checkDomainExists();

        $eppDomainInfo = $eppData['info'];
        $expiry = Carbon::parse($eppDomainInfo['expiry'])->valueOf();
        $status = $this->jobRecord->updateType !== DomainJobTypes::UPDATE_POST_AUTO_RENEWAL_GRACE_PERIOD
            ? DomainStatus::ACTIVE : DomainStatus::EXPIRED;

        $payload = [
            'id' => $this->jobRecord->domain->id,
            'name' => $this->jobRecord->domain->name,
            'status' => $status,
            'registrant' => $eppDomainInfo['registrant'],
            'expiry' => $expiry,
            'contacts' => json_encode($eppDomainInfo['contacts']),
            'client_status' => json_encode($eppDomainInfo['status']),
            'nameservers' => json_encode($eppDomainInfo['nameservers']),
        ];

        return $this->updateDomain($payload, $this->jobRecord->email);
    }

    private function updateDomain(array $domain, string $email): bool
    {
        $id = $domain['id'];
        unset($domain['id']);
        $name = $domain['name'];
        unset($domain['name']);

        $updated = Domain::when(is_int($id), function ($query) use ($id) {
            return $query->where('id', $id);
        })->update($domain);

        if ($updated) {
            app(AuthLogger::class)->info($this->fromWho('Updated domain'.$name, $email));
        }

        return $updated;
    }

    private function checkDomainExists(): array
    {
        app(AuthLogger::class)->info($this->fromWho('Checking if domain already exists...', $this->jobRecord->email));
        try {
            $response = EppDomainService::instance()->callDatastoreDomainInfo($this->jobRecord->name);
        } catch (Exception $e) {
            throw new Exception(QueueErrorTypes::RETRY);
        }

        return $this->evaluateDomainExistsResponse($response);
    }

    private function evaluateDomainExistsResponse(array $response)
    {
        $status = $this->getResponseStatus($response);

        if ($status === Config::get('domain.status.ok')) {
            return $this->getEppInfo($response);
        }

        throw new Exception(QueueErrorTypes::RETRY);
    }

    private function getResponseStatus(array $response)
    {
        return array_key_exists('status', $response) ? $response['status'] : Config::get('domain.status.error');
    }

    private function getEppInfo(array $response)
    {
        if (array_key_exists('data', $response)) {
            return [
                'exists' => true,
                'info' => $response['data'],
            ];
        }

        throw new Exception(QueueErrorTypes::RETRY);
    }
}
