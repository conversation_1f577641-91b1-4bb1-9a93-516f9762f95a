import { MdAppRegistration, MdAssignmentReturn } from "react-icons/md";
import _PaymentSummary from "../../Constant/_PaymentSummary";


const STATUS_REFUND = 'REFUNDED';
const STATUS_REFUND_REQUESTED = 'Refund Requested';

export default function PaymentItem({ item}) {
    const convertNameFormat = (str) => {
        str = str.toLowerCase();
        const words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));
        return words.join(' ');
    }

    const getItemAmount = (item) => {
        return parseFloat(item.total_domain_amount ?? 0).toFixed(2);

    }

    return (
        <div className={`" space-y-2" }`}>
            <div className="flex items-center justify-between border-gray-200">
                <div className="flex items-center justify-between hover:bg-gray-50 relative">
                    <span className=" text-2xl">{item.name}</span>
                    {(item.marketplace_payment_invoice_id) && <span className="ml-2 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm flex items-center">
                        Premium
                    </span>}
                </div>
                <span>
                    ${getItemAmount(item)} / {item.year_length}{" "}
                    {item.year_length > 1 ? "years" : "year"}
                </span>
            </div>
            <div className="flex justify-end text-gray-600 border-b border-gray-200 pb-2 mb-4">
                <span>
                    ICANN Fee: $ {parseFloat(item.total_icann_fee).toFixed(2)}
                </span>
            </div>
            <div
                className="flex items-center justify-between hover:bg-gray-50 relative"
            >
                <div className="flex items-center">
                    <MdAppRegistration className=" text-lg" />
                    <span className="ml-2 text-md text-gray-600">
                        {(item.node_type) && convertNameFormat(item.node_type)}
                    </span>
                    <span className="ml-2 text-md text-gray-600">
                        {item.node_status && ((item.node_status === STATUS_REFUND) ? STATUS_REFUND_REQUESTED : convertNameFormat(item.node_status))}
                    </span>
                </div>
                {(item.node_status == 'FAILED') &&
                    <div className="flex items-center">
                        <MdAssignmentReturn className="text-lg text-danger" />
                        <span className="ml-2 text-md text-danger">
                            Reimbursement {item.reimbursement_status && convertNameFormat(item.reimbursement_status)}
                        </span>
                    </div>
                }
            </div>
        </div>
    );
}
