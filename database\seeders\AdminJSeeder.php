<?php

namespace Database\Seeders;

use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AdminJSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (strcmp(env('APP_ENV'), 'local') != 0) {
            echo 'DatabaseSeeder::class ->Seeder has been disabled.' . PHP_EOL;

            return;
        }

        $this->newUserContact(4);
        $this->newRegisteredDomain(4);
    }

    // seed user contact
    private function newUserContact($userId)
    {
        $now = DB::raw('CURRENT_TIMESTAMP');

        $contact = [
            'name' => fake()->firstName() . ' ' . fake()->lastName(),
            'registry_contact' => 'jeasuson101',
            'email' => '<EMAIL>',
            'street' => 'Bonifacio St',
            'city' => 'Davao City',
            'state_province' => 'Davao del Sur',
            'postal_code' => '8000',
            'country_code' => 'PH',
            'status' => ContactStatus::REGISTERED,
            'ext_voice_number' => '1',
            'ext_fax_number' => '1',
            'voice_number' => '+111.1111111111',
            'fax_number' => '+111.1111111111',
            'organization_name' => fake()->company(),

        ];

        $userContact = [
            'user_id' => $userId,
            'contact_id' => 0,
            'registry_id' => 1,
            'default_registrar_contact' => true,
            'default_administrative_contact' => true,
            'default_technical_contact' => true,
            'default_billing_contact' => true,
        ];

        $contactId1 = DB::table('contacts')->insertGetId($contact);
        $contactId2 = DB::table('contacts')->insertGetId($contact);
        $userContact['contact_id'] = $contactId1;
        DB::table('user_contacts')->insert($userContact);
        $userContact['contact_id'] = $contactId2;
        $userContact['registry_id'] = 2;
        DB::table('user_contacts')->insert($userContact);

        $contact['name'] = 'Jasmine';
        $contact['email'] = '<EMAIL>';
        $contact['registry_contact'] = 'jeasuson109';
        $contactId3 = DB::table('contacts')->insertGetId($contact);
        $contactId4 = DB::table('contacts')->insertGetId($contact);

        $userContact['default_registrar_contact'] = false;
        $userContact['default_administrative_contact'] = false;
        $userContact['default_technical_contact'] = false;
        $userContact['default_billing_contact'] = false;

        $userContact['contact_id'] = $contactId3;
        $userContact['registry_id'] = 1;
        DB::table('user_contacts')->insert($userContact);

        $userContact['contact_id'] = $contactId4;
        $userContact['registry_id'] = 2;
        DB::table('user_contacts')->insert($userContact);
    }

    // seed registered domains
    private function newRegisteredDomain($userId)
    {
        $domain_names_com = [
            'hitotheworld.com',
            'sourgrape.com',
            'chooseyourweb.com',
            'whysoserious2.com',
            'whysoserious1.com',
            'mindlessfly.com',
            'chipsbyc.com',
            'cdforfree.com',
            'redmonday.com',
            'biogenesis.com',
            'jcdonutsph.com',
            'sumasamasila.com',
            'coffedose.com',
            'coffeedose.com',
            'cometcrash.com',
            'buy1get1.com',
            'lighthumors.com',
            'quantumquake.com',
            'novanudge.com',
            'lovemelikealovesong.com',
        ];

        $domain_names_net = [
            'namethatsaint.net',
            'chooseyourweb.net',
            'whysoserious.net',
            'getmobilegames123.net',
            'selectmobilegames.net',
            'whysoserious2.net',
            'chipsbyp.net',
            'lorebyaudio.net',
            'thosearetimes.net',
            'orasistime.net',
            'cdforfree.net',
            'redmonday.net',
            'biogenesis.net',
            'jcdonutsph.net',
            'sumasamasila.net',
            'makeamug.net',
            'coffeedosage.net',
            'coffeedose.net',
            'lighthumors.net',
            'winddleerrr.net',
            'lovemelikealovesong.net',
        ];

        $domain_names_org = [
            'chooseyourweb.org',
            'domainsphere.org',
            'getmobilegames123.org',
            'whysoserious1.org',
            'strangedomaintest9.org',
            'whysoserious2.org',
            'whysoserious.org',
            'mindlessfly.org',
            'lorebyaudio.org',
            'thosearetimes.org',
            'cdforfree.org',
            'redmonday.org',
            'biogenesis.org',
            'sumasamasila.org',
            'coffedose.org',
            'cometcrash.org',
            'lovemelikealovesong.org',
        ];

        $userContacts = DB::table('user_contacts')
            ->join('registries', 'registries.id', '=', 'user_contacts.registry_id')
            ->join('tlds', 'tlds.registry_id', '=', 'registries.id')
            ->join('extensions', 'extensions.id', '=', 'tlds.extension_id')
            ->join('contacts', 'contacts.id', '=', 'user_contacts.contact_id')
            ->where([['user_contacts.user_id', $userId], ['user_contacts.default_registrar_contact', true]])
            ->select(
                'user_contacts.id',
                'tlds.id as tld_id',
                'contacts.registry_contact as contact_name',
                'extensions.id as extension_id',
                'extensions.name as extension_name'
            )->get();

        $userContacts->each(function ($col, $key) use (
            $domain_names_net,
            $domain_names_org,
            $domain_names_com,
            $userId
        ) {
            if ($col->tld_id == 1) {
                $this->createDomains($col, $domain_names_com, $userId);
            }

            if ($col->tld_id == 2) {
                $this->createDomains($col, $domain_names_net, $userId);
            }

            if ($col->tld_id == 3) {
                $this->createDomains($col, $domain_names_org, $userId);
            }
        });
    }

    private function createDomains($userContact, $domain_names, $userId)
    {
        $categoryId = $this->getCategory($userId);
        $now = DB::raw('CURRENT_TIMESTAMP');

        $statusArray = EppDomainStatus::CLIENT_LOCK_STATUS;

        $domain = [
            'status' => DomainStatus::ACTIVE,
            'year_length' => 1,
            'expiry' => Carbon::now()->addYear()->valueOf(),
            'client_status' => json_encode($statusArray),
        ];

        $lockinPeriod = intval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_LOCKIN_PERIOD));

        $contactsArray = [
            DomainContact::REGISTRANT => $userContact->contact_name,
            DomainContact::ADMIN => $userContact->contact_name,
            DomainContact::TECH => $userContact->contact_name,
            DomainContact::BILLING => $userContact->contact_name,
        ];

        $contactsId = [
            DomainContact::REGISTRANT => $userContact->id,
            DomainContact::ADMIN => $userContact->id,
            DomainContact::TECH => $userContact->id,
            DomainContact::BILLING => $userContact->id,
        ];

        foreach ($domain_names as $name) {
            $domain['name'] = $name;
            $domain['status'] = DomainStatus::ACTIVE;
            $domain['root'] = $userContact->tld_id;
            $domain['registrant'] = $userContact->contact_name;
            $domain['contacts'] = json_encode($contactsArray);
            $domain['created_at'] = $now;
            $domain['updated_at'] = $now;
            $domain['server_renew_at'] = $now;
            $domain['client_renew_at'] = $now;
            $domainId = DB::table('domains')->insertGetId($domain);

            $registered_domain = [
                'user_contact_registrar_id' => $userContact->id,
                'extension_id' => $userContact->extension_id,
                'domain_id' => $domainId,
                'status' => UserDomainStatus::OWNED,
                'locked_until' => Carbon::now()->addDays($lockinPeriod)->timestamp,
                'contacts_id' => json_encode($contactsId),
                'user_category_id' => $categoryId,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            $rdId = DB::table('registered_domains')->insertGetId($registered_domain);

            // DB::table('registered_domains')->insert();
        }
    }

    private static function getCategory($userId)
    {
        return DB::table('user_categories')
            ->where('user_id', $userId)
            ->where('is_default', true)
            ->value('id');
    }
}
